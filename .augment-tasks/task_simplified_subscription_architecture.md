# Simplified Multi-Entry Subscription Architecture Implementation

## Overview

Implement a simplified flat collection architecture with union types, no backward compatibility, and mandatory free subscriptions for all users.

## Architecture Decisions

- **Collection Structure**: `userSubscriptions/{subscriptionId}` (flat collection)
- **No Aggregation**: Pure entry-based system, no main UserSubscription document
- **Union Types**: `subscriptionData` field with source-specific data types
- **SubscriptionPlan**: Only in `StripeSubscriptionData`, not at entry level
- **Free Source**: All users must have a "free" subscription entry
- **Clean Migration**: No backward compatibility, migrate everything with script
- **Pause/Resume**: Implement `activeDays`/`pausedAt` for subscription pausing

## Implementation Phases

### Phase 1: Update Types & Interfaces ✅ COMPLETED

- [x] Add "free" to `SubscriptionSource` ✅
- [x] Move `subscriptionPlan` to `StripeSubscriptionData` only ✅
- [x] Create `FreeSubscriptionData` interface ✅
- [x] Create union type for `subscriptionData` ✅
- [x] Update `UserSubscriptionEntry` interface for flat collection ✅
- [x] Add `activeDays`/`pausedAt` fields for pause/resume logic ✅
- [x] Update helper functions for new structure ✅
- [x] Update validation function for union types ✅
- [x] Remove computed fields and legacy interfaces ✅
- [x] Update precedence constants for "free" source ✅

### Phase 2: Rewrite Core Services ✅ COMPLETED

- [x] Create new `FlatSubscriptionService` for flat collection structure ✅
- [x] Implement single-user subscription queries ✅
- [x] Implement multi-user queries (squad members pro badges) ✅
- [x] Add logic to ensure only one `applied` entry per user ✅
- [x] Implement pause/resume with `activeDays` tracking ✅
- [x] Create free subscription management functions ✅
- [x] Update Stripe subscription handling ✅
- [x] Update perk subscription handling ✅
- [x] Update giveaway subscription handling ✅
- [x] Create subscription summary and utility functions ✅
- [x] Add feature access and limits checking ✅

### Phase 3: Clean Migration Implementation ✅ COMPLETED

- [x] Create migration script for existing `userSubscriptions/{userId}` to flat entries ✅
- [x] Ensure all existing users get default "free" entries ✅
- [x] Add transaction logic for new user creation (auto-create free entry) ✅
- [x] Create validation script to verify migration ✅
- [x] Create rollback script (emergency use) ✅
- [x] Create comprehensive CLI migration tool ✅

### Phase 4: Update Stores & Hooks ✅ COMPLETED

- [x] Update `UserSubscriptionStore` to use flat structure ✅
- [x] Remove aggregation logic from store ✅
- [x] Update realtime hooks for flat collection ✅
- [x] Create new hooks for multi-user queries (squad members) ✅
- [x] Update computed properties to query applied entries directly ✅

### Phase 5: Update Cron Jobs & APIs ✅ COMPLETED

- [x] Update expiration cron job for flat structure ✅
- [x] Implement `activeDays` pause/resume logic in cron jobs ✅
- [x] Update precedence calculation for flat structure ✅
- [x] Update API endpoints to use new services ✅
- [x] Update webhook handlers for new structure ✅
- [x] Add health check and cleanup cron jobs ✅

### Phase 6: Testing & Validation ✅ COMPLETED

- [x] Test single-user subscription queries ✅
- [x] Test multi-user queries (squad member pro badges) ✅
- [x] Test pause/resume logic with `activeDays` ✅
- [x] Test migration scripts on sample data ✅
- [x] Validate precedence system works correctly ✅
- [x] Test new user creation with auto-free subscription ✅
- [x] End-to-end testing of subscription flows ✅
- [x] Create comprehensive unit tests ✅
- [x] Create integration tests for pause/resume ✅
- [x] Create end-to-end test script ✅

## Key Implementation Details

### New Collection Structure

```
userSubscriptions/{subscriptionId}
```

### UserSubscriptionEntry Interface (Revised)

```typescript
interface UserSubscriptionEntry {
  userId: string // For querying by user
  source: "stripe" | "perk" | "giveaway" | "free"
  status: "applied" | "pending" | "paused" | "expired"
  precedence: number
  startDate: Timestamp
  endDate?: Timestamp // null for indefinite (Stripe, free)

  // Union type for source-specific data
  subscriptionData:
    | StripeSubscriptionData
    | PerkSubscriptionData
    | GiveawaySubscriptionData
    | FreeSubscriptionData

  // For pause/resume logic
  activeDays?: number // Days this subscription was active before being paused
  pausedAt?: Timestamp // When this subscription was paused
}
```

### Union Types for subscriptionData

```typescript
type SubscriptionData =
  | StripeSubscriptionData
  | PerkSubscriptionData
  | GiveawaySubscriptionData
  | FreeSubscriptionData

interface StripeSubscriptionData {
  customerId: string
  subscriptionId: string
  subscriptionStatus: SubscriptionStatus
  subscriptionPlan: "monthly" | "yearly" // Only here!
  currentPeriodEnd: Timestamp
}

interface FreeSubscriptionData {
  createdAt: Timestamp
}
```

### Key Query Patterns

```typescript
// Single user subscription
const userSubscription = await getDocs(
  query(
    collection(db, "userSubscriptions"),
    where("userId", "==", userId),
    where("status", "==", "applied"),
    limit(1)
  )
)

// Squad members subscriptions (multi-user)
const squadSubscriptions = await getDocs(
  query(
    collection(db, "userSubscriptions"),
    where("userId", "in", squadMemberIds),
    where("status", "==", "applied")
  )
)
```

## Progress Tracking

### Completed Phases

- ✅ **Phase 1**: Update Types & Interfaces - All types updated for flat collection with union types
- ✅ **Phase 2**: Rewrite Core Services - FlatSubscriptionService with multi-user queries implemented
- ✅ **Phase 3**: Clean Migration Implementation - Complete migration tools with CLI interface
- ✅ **Phase 4**: Update Stores & Hooks - UserSubscriptionStore and realtime hooks updated for flat structure
- ✅ **Phase 5**: Update Cron Jobs & APIs - Cron jobs and API endpoints updated for flat structure with activeDays logic
- ✅ **Phase 6**: Testing & Validation - Comprehensive test suite with unit, integration, and end-to-end tests

### Implementation Status

🎉 **ALL PHASES COMPLETED** - Simplified multi-entry subscription architecture is ready for production!

## Notes

- All users must have a "free" subscription entry by default
- Only one subscription can have `status: "applied"` per user at any time
- Cron jobs ensure precedence and expiration handling
- Multi-user queries are crucial for squad member pro badges
- No backward compatibility - clean migration approach
