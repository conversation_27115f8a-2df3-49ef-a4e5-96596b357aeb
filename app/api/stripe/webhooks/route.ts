import { NextRequest, NextResponse } from "next/server"
import { stripe } from "@/lib/server/stripe"
import { getAdminInstance } from "@/lib/firebase-admin"
import Strip<PERSON> from "stripe"
import { FlatSubscriptionWebhooks } from "@/lib/domains/user-subscription/flat-subscription.webhooks"

// Function to find a user by Stripe customer ID
async function getUserByStripeCustomerId(customerId: string) {
  try {
    const { adminDb } = await getAdminInstance()
    if (!adminDb) {
      console.error("Firebase Admin Firestore is not initialized")
      return null
    }

    // First try to find in userSubscriptions collection
    const subscriptionsRef = adminDb.collection("userSubscriptions")
    let snapshot = await subscriptionsRef.where("stripeCustomerId", "==", customerId).limit(1).get()

    if (!snapshot.empty) {
      const subscriptionDoc = snapshot.docs[0]
      const userId = subscriptionDoc.id // Same as userId in userSubscriptions
      console.log(`Found user ${userId} in userSubscriptions with customer ID: ${customerId}`)
      return { uid: userId }
    }

    // Fallback to users collection for backward compatibility
    console.log(`No subscription found with customer ID: ${customerId}, checking users collection`)
    const usersRef = adminDb.collection("users")
    snapshot = await usersRef.where("stripeCustomerId", "==", customerId).limit(1).get()

    if (snapshot.empty) {
      console.log(`No user found with Stripe customer ID: ${customerId}`)
      return null
    }

    const userDoc = snapshot.docs[0]
    return { ...userDoc.data(), uid: userDoc.id }
  } catch (error) {
    console.error("Error finding user by Stripe customer ID:", error)
    return null
  }
}

export async function POST(request: NextRequest) {
  console.log("🔥 WEBHOOK RECEIVED - Starting webhook processing")
  const body = await request.text()
  const signature = request.headers.get("stripe-signature") as string

  console.log("📝 Webhook body length:", body.length)
  console.log("🔑 Signature present:", !!signature)

  if (!signature) {
    console.log("❌ Missing stripe-signature header")
    return NextResponse.json({ error: "Missing stripe-signature header" }, { status: 400 })
  }

  try {
    console.log("🔐 Attempting to verify webhook signature...")
    // Verify the webhook signature
    const event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    )

    console.log("✅ Webhook signature verified successfully")
    console.log("📋 Event type:", event.type)
    console.log("🆔 Event ID:", event.id)

    // Handle the event
    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object as any
        let userId = session.metadata?.userId
        const customerId = session.customer
        const subscriptionId = session.subscription

        console.log(`Checkout session completed: ${session.id}`)
        console.log(`Customer ID: ${customerId}, Subscription ID: ${subscriptionId}`)

        // Update the user's subscription information
        if (customerId && subscriptionId) {
          // If userId is not in metadata, try to find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)
            } else {
              console.error(`Could not find user for customer ID ${customerId}`)
              break
            }
          }

          // Get the subscription details
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }

          // Get current_period_end from Stripe and convert to Date
          console.log(
            `Stripe current_period_end:`,
            subscription.current_period_end,
            typeof subscription.current_period_end
          )

          let currentPeriodEnd = null
          if (subscription.current_period_end) {
            // Ensure it's a number before multiplying
            const periodEndTimestamp = Number(subscription.current_period_end)
            if (!isNaN(periodEndTimestamp)) {
              currentPeriodEnd = new Date(periodEndTimestamp * 1000)
              console.log(`Converted to Date:`, currentPeriodEnd)
            } else {
              console.error(
                `Invalid current_period_end from Stripe:`,
                subscription.current_period_end
              )
            }
          }

          // Add userId to subscription metadata if it's not there
          if (!subscription.metadata?.userId) {
            console.log(`Adding userId ${userId} to subscription metadata`)
            await stripe.subscriptions.update(subscriptionId, {
              metadata: { userId },
            })
          }

          // Get customer details and add userId to customer metadata if not there
          let customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer
          if (!customer.metadata?.userId) {
            console.log(`Adding userId ${userId} to customer metadata`)
            customer = (await stripe.customers.update(customerId, {
              metadata: { userId },
            })) as Stripe.Customer
          }

          // Use the new flat subscription webhook handler
          const result = await FlatSubscriptionWebhooks.handleSubscriptionCreated(
            subscription,
            customer
          )

          if (!result.success) {
            console.error(
              `Failed to create flat subscription for user ${userId}:`,
              result.error?.message
            )
            // Return error response - no fallback needed
            return NextResponse.json(
              { error: "Failed to process subscription creation", message: result.error?.message },
              { status: 500 }
            )
          } else {
            console.log(`Successfully created flat subscription for user ${userId}`)
          }
        } else {
          console.error("Missing customer ID or subscription ID in checkout session")
        }
        break
      }

      case "customer.subscription.updated": {
        const subscription = event.data.object as any & { current_period_end?: number }
        const customerId = subscription.customer
        const subscriptionId = subscription.id
        const status = subscription.status

        console.log(`Subscription updated: ${subscriptionId}, Status: ${status}`)

        // Get current_period_end from Stripe and convert to Date
        console.log(
          `Stripe current_period_end:`,
          subscription.current_period_end,
          typeof subscription.current_period_end
        )

        let currentPeriodEnd = null
        if (subscription.current_period_end) {
          // Ensure it's a number before multiplying
          const periodEndTimestamp = Number(subscription.current_period_end)
          if (!isNaN(periodEndTimestamp)) {
            currentPeriodEnd = new Date(periodEndTimestamp * 1000)
            console.log(`Converted to Date:`, currentPeriodEnd)
          } else {
            console.error(
              `Invalid current_period_end from Stripe:`,
              subscription.current_period_end
            )
          }
        }

        // Try to get userId from metadata first
        let userId = subscription.metadata?.userId

        // If not in metadata, find the user by customer ID
        if (!userId) {
          console.log("No userId in metadata, searching by customer ID")
          const user = await getUserByStripeCustomerId(customerId)
          if (user) {
            userId = user.uid
            console.log(`Found user ${userId} by customer ID ${customerId}`)

            // Add userId to subscription metadata for future events
            await stripe.subscriptions.update(subscriptionId, {
              metadata: { userId },
            })
          }
        }

        if (userId) {
          // Get customer details and ensure userId is in metadata
          let customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer
          if (!customer.metadata?.userId) {
            console.log(`Adding userId ${userId} to customer metadata for update webhook`)
            customer = (await stripe.customers.update(customerId, {
              metadata: { userId },
            })) as Stripe.Customer
          }

          // Use the new flat subscription webhook handler
          const result = await FlatSubscriptionWebhooks.handleSubscriptionUpdated(
            subscription,
            customer
          )

          if (!result.success) {
            console.error(
              `Failed to update flat subscription for user ${userId}:`,
              result.error?.message
            )
            // Return error response - no fallback needed
            return NextResponse.json(
              { error: "Failed to process subscription update", message: result.error?.message },
              { status: 500 }
            )
          } else {
            console.log(
              `Successfully updated flat subscription status to ${status} for user ${userId}`
            )
          }
        } else {
          console.error(`Could not find user for customer ID ${customerId}`)
        }
        break
      }

      case "customer.subscription.deleted": {
        const subscription = event.data.object as any
        const customerId = subscription.customer
        const subscriptionId = subscription.id

        console.log(`Subscription deleted: ${subscriptionId}`)

        // Try to get userId from metadata first
        let userId = subscription.metadata?.userId

        // If not in metadata, find the user by customer ID
        if (!userId) {
          console.log("No userId in metadata, searching by customer ID")
          const user = await getUserByStripeCustomerId(customerId)
          if (user) {
            userId = user.uid
            console.log(`Found user ${userId} by customer ID ${customerId}`)
          }
        }

        if (userId) {
          // Get customer details for the flat subscription webhook
          const customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer

          // Use the new flat subscription webhook handler
          const result = await FlatSubscriptionWebhooks.handleSubscriptionDeleted(
            subscription,
            customer
          )

          if (!result.success) {
            console.error(
              `Failed to delete flat subscription for user ${userId}:`,
              result.error?.message
            )
            // Return error response - no fallback needed
            return NextResponse.json(
              { error: "Failed to process subscription deletion", message: result.error?.message },
              { status: 500 }
            )
          } else {
            console.log(`Successfully deleted flat subscription for user ${userId}`)
          }
        } else {
          console.error(`Could not find user for customer ID ${customerId}`)
        }
        break
      }

      case "invoice.payment_succeeded": {
        const invoice = event.data.object as any
        const customerId = invoice.customer
        const subscriptionId = invoice.subscription

        console.log(`Invoice payment succeeded: ${invoice.id}`)

        if (subscriptionId) {
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }

          // Try to get userId from metadata first
          let userId = subscription.metadata?.userId

          // If not in metadata, find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)

              // Add userId to subscription metadata for future events
              await stripe.subscriptions.update(subscriptionId, {
                metadata: { userId },
              })
            }
          }

          if (userId) {
            // Get current_period_end from Stripe and convert to Date
            console.log(
              `Stripe current_period_end:`,
              subscription.current_period_end,
              typeof subscription.current_period_end
            )

            let currentPeriodEnd = null
            if (subscription.current_period_end) {
              // Ensure it's a number before multiplying
              const periodEndTimestamp = Number(subscription.current_period_end)
              if (!isNaN(periodEndTimestamp)) {
                currentPeriodEnd = new Date(periodEndTimestamp * 1000)
                console.log(`Converted to Date:`, currentPeriodEnd)
              } else {
                console.error(
                  `Invalid current_period_end from Stripe:`,
                  subscription.current_period_end
                )
              }
            }
            // Get customer details for the flat subscription webhook
            const customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer

            // Use the new flat subscription webhook handler
            const result = await FlatSubscriptionWebhooks.handleInvoicePaymentSucceeded(
              invoice,
              customer
            )

            if (!result.success) {
              console.error(
                `Failed to update flat subscription after payment for user ${userId}:`,
                result.error?.message
              )
              // Return error response - no fallback needed
              return NextResponse.json(
                { error: "Failed to process invoice payment", message: result.error?.message },
                { status: 500 }
              )
            } else {
              console.log(`Successfully updated flat subscription after payment for user ${userId}`)
            }
          } else {
            console.error(`Could not find user for customer ID ${customerId}`)
          }
        } else {
          console.log("No subscription ID in invoice, might be a one-time payment")
        }
        break
      }

      case "invoice.payment_failed": {
        const invoice = event.data.object as any
        const customerId = invoice.customer
        const subscriptionId = invoice.subscription

        console.log(`Invoice payment failed: ${invoice.id}`)

        if (subscriptionId) {
          const subscription = (await stripe.subscriptions.retrieve(
            subscriptionId
          )) as Stripe.Subscription & { current_period_end?: number }

          // Try to get userId from metadata first
          let userId = subscription.metadata?.userId

          // If not in metadata, find the user by customer ID
          if (!userId) {
            console.log("No userId in metadata, searching by customer ID")
            const user = await getUserByStripeCustomerId(customerId)
            if (user) {
              userId = user.uid
              console.log(`Found user ${userId} by customer ID ${customerId}`)

              // Add userId to subscription metadata for future events
              await stripe.subscriptions.update(subscriptionId, {
                metadata: { userId },
              })
            }
          }

          if (userId) {
            // Get customer details for the flat subscription webhook
            const customer = (await stripe.customers.retrieve(customerId)) as Stripe.Customer

            // Use the new flat subscription webhook handler
            const result = await FlatSubscriptionWebhooks.handleInvoicePaymentFailed(
              invoice,
              customer
            )

            if (!result.success) {
              console.error(
                `Failed to update flat subscription to past_due for user ${userId}:`,
                result.error?.message
              )
              // Return error response - no fallback needed
              return NextResponse.json(
                {
                  error: "Failed to process invoice payment failure",
                  message: result.error?.message,
                },
                { status: 500 }
              )
            } else {
              console.log(`Successfully updated flat subscription to past_due for user ${userId}`)
            }
          } else {
            console.error(`Could not find user for customer ID ${customerId}`)
          }
        } else {
          console.log("No subscription ID in invoice")
        }
        break
      }

      default:
        console.log(`Unhandled event type: ${event.type}`)
    }

    console.log("🎉 Webhook processed successfully, event type:", event.type)
    return NextResponse.json({ received: true, type: event.type })
  } catch (error) {
    console.error("💥 Error handling webhook:", error)

    // Check if this is a webhook signature verification error
    if (error instanceof Stripe.errors.StripeSignatureVerificationError) {
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 })
    }

    const errorMessage = error instanceof Error ? error.message : "Unknown error"

    return NextResponse.json(
      { error: "Failed to handle webhook", message: errorMessage },
      { status: 500 }
    )
  }
}
