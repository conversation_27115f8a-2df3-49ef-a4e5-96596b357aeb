# dependencies
/node_modules
/.pnp
.pnp.js
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# pnpm
.pnpm-store/
.pnpm-debug.log*

# testing
/coverage

# next.js
/.next/
/out/
.next/
next-env.d.ts

# production
/build
/dist

# misc
.DS_Store
*.pem
Thumbs.db

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env
.env.local
.env.vercel
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# PWA files (generated by next-pwa)
public/sw.js
public/sw.js.map
public/workbox-*.js
public/workbox-*.js.map

# typescript
*.tsbuildinfo

# IDE / editors
.idea/
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
*.sublime-project
*.sublime-workspace
*.swp
*.swo

# logs
logs
*.log

# turbo
.turbo

# cache
.eslintcache
.stylelintcache

# Added by Task Master AI
# Logs
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
# Editor directories and files
.idea
.vscode
.roo
.cursor
.taskmaster
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.roomodes
.windsurfrules
.augment-tasks
# OS specific
