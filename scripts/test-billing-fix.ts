#!/usr/bin/env npx tsx

/**
 * Simple test script to verify the billing settings fix
 * Tests that users with both perk and Stripe subscriptions can access the customer portal
 */

import { FlatSubscriptionService } from '../lib/domains/user-subscription/flat-subscription.service'

async function testBillingFix() {
  console.log('🧪 Testing billing settings fix...')
  
  // Test user ID (replace with actual test user ID)
  const testUserId = 'S772DOQitIatD6NUh4TaAENydY32'
  
  try {
    // Test 1: Get current subscription
    console.log('\n1️⃣ Testing getCurrentSubscription...')
    const currentSubscription = await FlatSubscriptionService.getCurrentSubscription(testUserId)
    console.log('Current subscription:', currentSubscription ? {
      id: currentSubscription.id,
      source: currentSubscription.source,
      status: currentSubscription.status,
      precedence: currentSubscription.precedence
    } : 'None')
    
    // Test 2: Get all subscriptions
    console.log('\n2️⃣ Testing getUserSubscriptions...')
    const allSubscriptions = await FlatSubscriptionService.getUserSubscriptions(testUserId)
    console.log('All subscriptions:')
    allSubscriptions.forEach((sub, index) => {
      console.log(`  ${index + 1}. ${sub.source} (${sub.status}) - precedence: ${sub.precedence}`)
      if (sub.source === 'stripe') {
        const stripeData = sub.subscriptionData as any
        console.log(`     Customer ID: ${stripeData.customerId}`)
        console.log(`     Subscription ID: ${stripeData.subscriptionId}`)
      }
    })
    
    // Test 3: Check if user has any Stripe subscription
    const hasStripeSubscription = allSubscriptions.some(sub => sub.source === 'stripe')
    console.log(`\n3️⃣ Has Stripe subscription: ${hasStripeSubscription}`)
    
    // Test 4: Check if current subscription is Stripe
    const hasActiveStripeSubscription = currentSubscription?.source === 'stripe'
    console.log(`4️⃣ Has active Stripe subscription: ${hasActiveStripeSubscription}`)
    
    // Test 5: Billing logic test
    console.log('\n5️⃣ Billing logic test:')
    console.log(`   Should show "Manage Subscription" button: ${hasStripeSubscription}`)
    console.log(`   Current subscription source: ${currentSubscription?.source || 'none'}`)
    
    if (hasStripeSubscription && !hasActiveStripeSubscription) {
      console.log('   ✅ CORRECT: User has Stripe subscription but it\'s paused due to higher precedence')
      console.log('   ✅ CORRECT: Billing settings should show manage button')
    } else if (hasStripeSubscription && hasActiveStripeSubscription) {
      console.log('   ✅ CORRECT: User has active Stripe subscription')
      console.log('   ✅ CORRECT: Billing settings should show manage button')
    } else {
      console.log('   ❌ User has no Stripe subscription')
    }
    
    console.log('\n✅ Test completed successfully!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    process.exit(1)
  }
}

// Run the test
testBillingFix()
