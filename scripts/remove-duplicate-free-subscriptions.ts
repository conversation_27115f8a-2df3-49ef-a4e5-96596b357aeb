#!/usr/bin/env tsx

/**
 * Remove Duplicate Free Subscription Entries Script
 *
 * This script removes duplicate free subscription entries that may have been created
 * during migration. It keeps only one free subscription entry per user (the oldest one).
 *
 * Usage:
 *   tsx scripts/remove-duplicate-free-subscriptions.ts [env-file]
 *
 * Examples:
 *   tsx scripts/remove-duplicate-free-subscriptions.ts --dry-run
 *   tsx scripts/remove-duplicate-free-subscriptions.ts .env.vercel
 *   tsx scripts/remove-duplicate-free-subscriptions.ts --validate-only
 */

import { config } from "dotenv"
import { resolve } from "path"
import { initializeApp, cert, getApps } from "firebase-admin/app"
import { getFirestore } from "firebase-admin/firestore"

// Parse command line arguments
const args = process.argv.slice(2)
const isDryRun = args.includes("--dry-run")
const isValidateOnly = args.includes("--validate-only")
const envFile = args.find((arg) => !arg.startsWith("--")) || ".env.vercel"

// Initialize Firebase Admin SDK
async function initializeFirebase() {
  // Load environment variables from specified .env file
  const envPath = resolve(process.cwd(), envFile)
  console.log(`Loading environment variables from: ${envPath}`)
  config({ path: envPath })

  // Initialize Firebase Admin SDK if not already initialized
  if (!getApps().length) {
    try {
      if (!process.env.FIREBASE_SERVICE_ACCOUNT_KEY) {
        throw new Error("FIREBASE_SERVICE_ACCOUNT_KEY environment variable is not set")
      }

      const serviceAccount = JSON.parse(process.env.FIREBASE_SERVICE_ACCOUNT_KEY)

      initializeApp({
        credential: cert(serviceAccount),
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      })

      console.log("✅ Firebase Admin SDK initialized successfully")
    } catch (error) {
      console.error("❌ Failed to initialize Firebase Admin SDK:", error)
      process.exit(1)
    }
  }
}

// Find and analyze duplicate free subscriptions
async function findDuplicateFreeSubscriptions() {
  const db = getFirestore()
  console.log("🔍 Analyzing free subscription entries...")

  try {
    // Get all subscription entries
    const subscriptionsSnapshot = await db.collection("userSubscriptions").get()
    
    // Group free subscriptions by userId
    const freeSubscriptionsByUser = new Map<string, any[]>()
    
    for (const doc of subscriptionsSnapshot.docs) {
      const data = doc.data()
      
      // Only process free subscription entries
      if (data.source === "free") {
        const userId = data.userId
        
        if (!freeSubscriptionsByUser.has(userId)) {
          freeSubscriptionsByUser.set(userId, [])
        }
        
        freeSubscriptionsByUser.get(userId)!.push({
          id: doc.id,
          ...data,
          createdAt: data.createdAt || data.startDate
        })
      }
    }

    // Find users with duplicate free subscriptions
    const duplicates = new Map<string, any[]>()
    
    for (const [userId, subscriptions] of freeSubscriptionsByUser.entries()) {
      if (subscriptions.length > 1) {
        // Sort by creation date (oldest first)
        subscriptions.sort((a, b) => {
          const aTime = a.createdAt?.toDate?.() || a.createdAt || new Date(0)
          const bTime = b.createdAt?.toDate?.() || b.createdAt || new Date(0)
          return aTime.getTime() - bTime.getTime()
        })
        
        duplicates.set(userId, subscriptions)
      }
    }

    console.log(`\n📊 Analysis Results:`)
    console.log(`   Total users with subscriptions: ${freeSubscriptionsByUser.size}`)
    console.log(`   Users with duplicate free subscriptions: ${duplicates.size}`)
    
    if (duplicates.size > 0) {
      console.log(`\n🔍 Duplicate Details:`)
      for (const [userId, subscriptions] of duplicates.entries()) {
        console.log(`   User ${userId}: ${subscriptions.length} free subscriptions`)
        subscriptions.forEach((sub, index) => {
          const createdAt = sub.createdAt?.toDate?.() || sub.createdAt || 'Unknown'
          const status = index === 0 ? '(KEEP)' : '(REMOVE)'
          console.log(`     - ${sub.id} - ${createdAt} - Status: ${sub.status} ${status}`)
        })
      }
    }

    return duplicates
  } catch (error) {
    console.error("❌ Error analyzing subscriptions:", error)
    throw error
  }
}

// Remove duplicate free subscriptions
async function removeDuplicateFreeSubscriptions(duplicates: Map<string, any[]>) {
  const db = getFirestore()
  let totalRemoved = 0
  
  console.log(`\n🧹 ${isDryRun ? 'DRY RUN: Would remove' : 'Removing'} duplicate free subscriptions...`)

  try {
    for (const [userId, subscriptions] of duplicates.entries()) {
      // Keep the first (oldest) subscription, remove the rest
      const toRemove = subscriptions.slice(1)
      
      console.log(`\n👤 Processing user ${userId}:`)
      console.log(`   Keeping: ${subscriptions[0].id} (${subscriptions[0].createdAt?.toDate?.() || 'Unknown date'})`)
      
      for (const subscription of toRemove) {
        console.log(`   ${isDryRun ? 'Would remove' : 'Removing'}: ${subscription.id} (${subscription.createdAt?.toDate?.() || 'Unknown date'})`)
        
        if (!isDryRun) {
          await db.collection("userSubscriptions").doc(subscription.id).delete()
        }
        totalRemoved++
      }
    }

    console.log(`\n✅ ${isDryRun ? 'Would remove' : 'Removed'} ${totalRemoved} duplicate free subscription entries`)
    
    if (isDryRun) {
      console.log("\n💡 Run without --dry-run to actually remove the duplicates")
    }

    return totalRemoved
  } catch (error) {
    console.error("❌ Error removing duplicates:", error)
    throw error
  }
}

// Validate the cleanup results
async function validateCleanup() {
  console.log("\n🔍 Validating cleanup results...")
  
  const duplicatesAfter = await findDuplicateFreeSubscriptions()
  
  if (duplicatesAfter.size === 0) {
    console.log("✅ Validation passed: No duplicate free subscriptions found")
  } else {
    console.log(`❌ Validation failed: Still found ${duplicatesAfter.size} users with duplicate free subscriptions`)
  }
  
  return duplicatesAfter.size === 0
}

// Main execution function
async function main() {
  try {
    console.log("🚀 Starting duplicate free subscription cleanup...")
    console.log(`Mode: ${isDryRun ? 'DRY RUN' : isValidateOnly ? 'VALIDATE ONLY' : 'LIVE'}`)
    
    await initializeFirebase()
    
    // Find duplicates
    const duplicates = await findDuplicateFreeSubscriptions()
    
    if (duplicates.size === 0) {
      console.log("\n✅ No duplicate free subscriptions found. Nothing to clean up!")
      return
    }
    
    if (isValidateOnly) {
      console.log("\n📋 Validation complete. Use without --validate-only to remove duplicates.")
      return
    }
    
    // Remove duplicates
    await removeDuplicateFreeSubscriptions(duplicates)
    
    // Validate if not dry run
    if (!isDryRun) {
      await validateCleanup()
    }
    
    console.log("\n🎉 Duplicate free subscription cleanup completed successfully!")
    
  } catch (error) {
    console.error("❌ Script failed:", error)
    process.exit(1)
  }
}

// Run the script
main()
